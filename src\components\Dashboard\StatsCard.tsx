
import React from 'react';
import { LucideIcon, TrendingUp, TrendingDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';

interface StatsCardProps {
  title: string;
  value: string;
  subtitle: string;
  icon: LucideIcon;
  color: 'primary' | 'secondary' | 'success' | 'neutral';
  progress?: number;
  trend?: 'up' | 'down';
  trendValue?: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  subtitle,
  icon: Icon,
  color,
  progress,
  trend,
  trendValue,
}) => {
  const { statsCards } = useColors();

  return (
    <div className={cn(
      'relative overflow-hidden bg-gradient-to-br rounded-xl p-6 border transition-all duration-200 hover:shadow-card-hover group',
      statsCards[color].className
    )}>
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10 group-hover:scale-110 transition-transform duration-300"></div>
      
      <div className="relative space-y-4">
        <div className="flex items-center justify-between">
          <div
            className="p-3 rounded-lg bg-white/50 dark:bg-black/20"
            style={{ color: statsCards[color].style.color }}
          >
            <Icon className="w-6 h-6" />
          </div>
          
          {trend && trendValue && (
            <div className={cn(
              'flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium',
              trend === 'up' 
                ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                : 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
            )}>
              {trend === 'up' ? (
                <TrendingUp className="w-3 h-3" />
              ) : (
                <TrendingDown className="w-3 h-3" />
              )}
              {trendValue}
            </div>
          )}
        </div>
        
        <div className="space-y-1">
          <h3 className="text-2xl font-bold text-foreground">{value}</h3>
          <p className="text-sm text-muted-foreground font-medium">{title}</p>
          <p className="text-xs text-muted-foreground">{subtitle}</p>
        </div>
        
        {progress !== undefined && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Progress</span>
              <span>{progress}%</span>
            </div>
            <div className="w-full bg-black/10 dark:bg-white/10 rounded-full h-2">
              <div
                className="h-full rounded-full transition-all duration-500"
                style={{
                  width: `${progress}%`,
                  backgroundColor: statsCards[color].style.color
                }}
              ></div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StatsCard;
