
import React from 'react';
import { Plus, Calendar, FileText, BarChart3, Settings } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';

const QuickActions: React.FC = () => {
  const { t, isRTL } = useLanguage();

  const actions = [
    {
      icon: Plus,
      title: t('tasks.newTask'),
      description: isRTL ? 'أنشئ مهمة جديدة بسرعة' : 'Create a new task quickly',
      color: 'from-sky-500 to-sky-600',
    },
    {
      icon: Calendar,
      title: t('projects.newProject'),
      description: isRTL ? 'ابدأ مشروعاً جديداً' : 'Start a new project',
      color: 'from-slate-500 to-slate-600',
    },
    {
      icon: FileText,
      title: t('notes.newNote'),
      description: isRTL ? 'اكتب ملاحظة سريعة' : 'Write a quick note',
      color: 'from-lavender-500 to-lavender-600',
    },
    {
      icon: BarChart3,
      title: t('analytics.title'),
      description: isRTL ? 'عرض إحصائيات الإنتاجية' : 'View productivity stats',
      color: 'from-blue-500 to-blue-600',
    },
  ];

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold">{isRTL ? 'إجراءات سريعة' : 'Quick Actions'}</h2>
      
      <div className="space-y-3">
        {actions.map((action, index) => (
          <button
            key={index}
            className={cn(
              "w-full p-4 rounded-xl border border-border bg-card hover:shadow-card-hover transition-all duration-200 group",
              isRTL && "text-right"
            )}
          >
            <div className={cn(
              "flex items-center gap-4",
              isRTL && "flex-row-reverse"
            )}>
              <div className={cn(
                "p-3 rounded-lg bg-gradient-to-r text-white group-hover:scale-110 transition-transform duration-200",
                action.color
              )}>
                <action.icon className="w-5 h-5" />
              </div>
              <div className={cn("flex-1", isRTL && "text-right")}>
                <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
                  {action.title}
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  {action.description}
                </p>
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="mt-8 p-4 bg-gradient-to-br from-blue-50 to-slate-50 dark:from-blue-900/20 dark:to-slate-900/20 rounded-xl border border-blue-200 dark:border-blue-700">
        <h3 className={cn(
          "font-semibold mb-3 text-blue-700 dark:text-blue-300",
          isRTL && "text-right"
        )}>
          {isRTL ? 'النشاط الأخير' : 'Recent Activity'}
        </h3>
        <div className="space-y-2">
          {[
            { text: isRTL ? 'أكملت مهمة "مراجعة التقارير"' : 'Completed task "Review reports"', time: '2h' },
            { text: isRTL ? 'أنشأت ملاحظة جديدة' : 'Created new note', time: '4h' },
            { text: isRTL ? 'أضفت مشروع "تطبيق الهاتف"' : 'Added project "Mobile App"', time: '1d' },
          ].map((activity, index) => (
            <div
              key={index}
              className={cn(
                "flex items-center justify-between text-sm",
                isRTL && "flex-row-reverse"
              )}
            >
              <span className="text-muted-foreground">{activity.text}</span>
              <span className="text-xs text-zenith-sage-500">{activity.time}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default QuickActions;
