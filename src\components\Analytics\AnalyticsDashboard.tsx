
import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, Pie<PERSON>hart, Pie, Cell } from 'recharts';
import { TrendingUp, Clock, CheckSquare, Calendar } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';

const AnalyticsDashboard: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { chart } = useColors();

  const weeklyData = [
    { name: isRTL ? 'السبت' : 'Sat', tasks: 4, hours: 6 },
    { name: isRTL ? 'الأحد' : 'Sun', tasks: 6, hours: 8 },
    { name: isRTL ? 'الاثنين' : 'Mon', tasks: 8, hours: 7 },
    { name: isRTL ? 'الثلاثاء' : 'Tue', tasks: 5, hours: 6 },
    { name: is<PERSON><PERSON> ? 'الأربعاء' : 'Wed', tasks: 9, hours: 9 },
    { name: isRTL ? 'الخميس' : 'Thu', tasks: 7, hours: 8 },
    { name: isRTL ? 'الجمعة' : 'Fri', tasks: 6, hours: 5 },
  ];

  const productivityData = [
    { name: isRTL ? 'مهام' : 'Tasks', value: 45, color: chart.primary },
    { name: isRTL ? 'مشاريع' : 'Projects', value: 30, color: chart.secondary },
    { name: isRTL ? 'ملاحظات' : 'Notes', value: 25, color: chart.success },
  ];

  const statsCards = [
    {
      title: t('analytics.tasksCompleted'),
      value: '47',
      subtitle: t('analytics.thisWeek'),
      icon: CheckSquare,
      trend: '+12%',
      color: 'from-green-500 to-green-600',
    },
    {
      title: t('analytics.timeSpent'),
      value: '32',
      subtitle: t('analytics.hours'),
      icon: Clock,
      trend: '+8%',
      color: 'from-blue-500 to-blue-600',
    },
    {
      title: t('analytics.projectsActive'),
      value: '7',
      subtitle: t('analytics.thisMonth'),
      icon: Calendar,
      trend: '+2',
      color: 'from-purple-500 to-purple-600',
    },
    {
      title: t('analytics.productivity'),
      value: '89%',
      subtitle: t('analytics.performance'),
      icon: TrendingUp,
      trend: '+5%',
      color: 'from-slate-500 to-slate-600',
    },
  ];

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className={cn(isRTL && "text-right")}>
        <h1 className="text-3xl font-bold">{t('analytics.title')}</h1>
        <p className="text-muted-foreground mt-2">{t('analytics.subtitle')}</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((stat, index) => (
          <div
            key={index}
            className="bg-card border border-border rounded-xl p-6 hover:shadow-card-hover transition-all duration-200"
          >
            <div className={cn(
              "flex items-center justify-between mb-4",
              isRTL && "flex-row-reverse"
            )}>
              <div className={cn(
                `p-3 rounded-lg bg-gradient-to-r ${stat.color} text-white`
              )}>
                <stat.icon className="w-6 h-6" />
              </div>
              <span className="text-xs px-2 py-1 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded-full font-medium">
                {stat.trend}
              </span>
            </div>
            
            <div className={cn(isRTL && "text-right")}>
              <h3 className="text-2xl font-bold text-foreground mb-1">{stat.value}</h3>
              <p className="text-sm font-medium text-muted-foreground mb-1">{stat.title}</p>
              <p className="text-xs text-muted-foreground">{stat.subtitle}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Weekly Performance */}
        <div className="bg-card border border-border rounded-xl p-6">
          <h2 className={cn(
            "text-xl font-bold mb-6",
            isRTL && "text-right"
          )}>
            {t('analytics.performance')} - {t('analytics.thisWeek')}
          </h2>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={weeklyData}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="name" 
                  tick={{ fontSize: 12 }}
                  className="text-muted-foreground"
                />
                <YAxis 
                  tick={{ fontSize: 12 }}
                  className="text-muted-foreground"
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                  }}
                />
                <Bar
                  dataKey="tasks"
                  fill={chart.primary}
                  radius={[4, 4, 0, 0]}
                  name={isRTL ? 'المهام' : 'Tasks'}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Productivity Distribution */}
        <div className="bg-card border border-border rounded-xl p-6">
          <h2 className={cn(
            "text-xl font-bold mb-6",
            isRTL && "text-right"
          )}>
            {t('analytics.productivity')} - {t('analytics.overview')}
          </h2>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={productivityData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {productivityData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
          
          {/* Legend */}
          <div className="flex justify-center gap-4 mt-4">
            {productivityData.map((item, index) => (
              <div key={index} className={cn(
                "flex items-center gap-2",
                isRTL && "flex-row-reverse"
              )}>
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-sm text-muted-foreground">{item.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Trends */}
      <div className="bg-card border border-border rounded-xl p-6">
        <h2 className={cn(
          "text-xl font-bold mb-6",
          isRTL && "text-right"
        )}>
          {t('analytics.trends')} - {t('analytics.thisMonth')}
        </h2>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={weeklyData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="name" 
                tick={{ fontSize: 12 }}
                className="text-muted-foreground"
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                className="text-muted-foreground"
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '8px',
                }}
              />
              <Line
                type="monotone"
                dataKey="tasks"
                stroke={chart.primary}
                strokeWidth={3}
                dot={{ fill: chart.primary, strokeWidth: 2, r: 6 }}
                name={isRTL ? 'المهام' : 'Tasks'}
              />
              <Line
                type="monotone"
                dataKey="hours"
                stroke={chart.secondary}
                strokeWidth={3}
                dot={{ fill: chart.secondary, strokeWidth: 2, r: 6 }}
                name={isRTL ? 'الساعات' : 'Hours'}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
