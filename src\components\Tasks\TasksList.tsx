
import React, { useState } from 'react';
import { CheckSquare, Plus, Filter, Search } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';
import { useColors } from '@/hooks/useColors';

const TasksList: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const { priority } = useColors();
  const [filter, setFilter] = useState('all');

  const tasks = [
    {
      id: '1',
      title: isRTL ? 'مراجعة التقارير الشهرية' : 'Review monthly reports',
      description: isRTL ? 'فحص وتحليل البيانات المالية للشهر الماضي' : 'Review and analyze last month financial data',
      priority: 'high',
      status: 'todo',
      dueDate: '2024-01-15',
      tags: ['تقارير', 'مالية'],
    },
    {
      id: '2',
      title: isRTL ? 'اجتماع فريق التطوير' : 'Development team meeting',
      description: isRTL ? 'مناقشة التحديثات الجديدة والمشاكل التقنية' : 'Discuss new updates and technical issues',
      priority: 'medium',
      status: 'inProgress',
      dueDate: '2024-01-16',
      tags: ['اجتماع', 'فريق'],
    },
  ];

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className={cn(
        "flex items-center justify-between",
        isRTL && "flex-row-reverse"
      )}>
        <div className={cn(isRTL && "text-right")}>
          <h1 className="text-3xl font-bold">{t('tasks.title')}</h1>
          <p className="text-muted-foreground mt-2">{t('tasks.subtitle')}</p>
        </div>
        <button className="flex items-center gap-2 px-4 py-2 bg-zenith-gradient text-white rounded-lg hover:shadow-zenith-lg transition-all duration-200">
          <Plus className="w-5 h-5" />
          {t('tasks.newTask')}
        </button>
      </div>

      {/* Filters */}
      <div className={cn(
        "flex items-center gap-4",
        isRTL && "flex-row-reverse"
      )}>
        <div className="flex gap-2">
          {['all', 'todo', 'inProgress', 'completed'].map((status) => (
            <button
              key={status}
              onClick={() => setFilter(status)}
              className={cn(
                "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200",
                filter === status
                  ? "bg-zenith-gradient text-white shadow-zenith"
                  : "bg-card hover:bg-accent text-foreground"
              )}
            >
              {t(`tasks.status.${status}` || status)}
            </button>
          ))}
        </div>
        
        <div className="flex gap-2 ml-auto">
          <button className="p-2 bg-card hover:bg-accent rounded-lg transition-colors">
            <Filter className="w-5 h-5" />
          </button>
          <button className="p-2 bg-card hover:bg-accent rounded-lg transition-colors">
            <Search className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Tasks Grid */}
      <div className="grid gap-4">
        {tasks.map((task) => (
          <div
            key={task.id}
            className="bg-card border border-border rounded-xl p-6 hover:shadow-card-hover transition-all duration-200"
          >
            <div className={cn(
              "flex items-start gap-4",
              isRTL && "flex-row-reverse"
            )}>
              <button className="flex-shrink-0 w-6 h-6 rounded border-2 border-muted-foreground hover:border-blue-400 transition-colors">
                <CheckSquare className="w-4 h-4" />
              </button>
              
              <div className={cn("flex-1", isRTL && "text-right")}>
                <h3 className="text-lg font-semibold mb-2">{task.title}</h3>
                <p className="text-muted-foreground mb-4">{task.description}</p>
                
                <div className={cn(
                  "flex items-center gap-4 text-sm",
                  isRTL && "flex-row-reverse"
                )}>
                  <span className={cn(
                    "px-2 py-1 rounded-full text-xs font-medium",
                    task.priority === 'high' ? priority.high.className :
                    task.priority === 'medium' ? priority.medium.className :
                    priority.low.className
                  )}>
                    {t(`tasks.priority.${task.priority}`)}
                  </span>
                  
                  <span className="text-muted-foreground">
                    {t('tasks.dueDate')}: {task.dueDate}
                  </span>
                  
                  <div className="flex gap-1">
                    {task.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-slate-100 dark:bg-slate-900/30 text-slate-700 dark:text-slate-300 rounded text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TasksList;
