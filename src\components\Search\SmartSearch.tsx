
import React, { useState } from 'react';
import { <PERSON>, Mic, MicOff } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';

const SmartSearch: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [isListening, setIsListening] = useState(false);

  const handleVoiceSearch = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.lang = isRTL ? 'ar-SA' : 'en-US';
      recognition.continuous = false;
      recognition.interimResults = false;

      recognition.onstart = () => {
        setIsListening(true);
      };

      recognition.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setSearchQuery(transcript);
        setIsListening(false);
      };

      recognition.onerror = () => {
        setIsListening(false);
      };

      recognition.onend = () => {
        setIsListening(false);
      };

      recognition.start();
    }
  };

  return (
    <div className={cn(
      "relative flex-1 max-w-2xl",
      isRTL && "flex-row-reverse"
    )}>
      <div className="relative">
        <Search className={cn(
          "absolute top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5",
          isRTL ? "right-3" : "left-3"
        )} />
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder={t('search.placeholder')}
          className={cn(
            "w-full bg-card border border-border rounded-lg py-3 px-12 text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200",
            isRTL ? "text-right pr-12 pl-16" : "text-left pl-12 pr-16"
          )}
        />
        <button
          onClick={handleVoiceSearch}
          disabled={isListening}
          className={cn(
            "absolute top-1/2 transform -translate-y-1/2 p-2 rounded-md hover:bg-accent transition-colors duration-200",
            isRTL ? "left-2" : "right-2",
            isListening && "text-primary animate-pulse"
          )}
          title={t('search.voice')}
        >
          {isListening ? (
            <MicOff className="w-4 h-4" />
          ) : (
            <Mic className="w-4 h-4" />
          )}
        </button>
      </div>
    </div>
  );
};

export default SmartSearch;
