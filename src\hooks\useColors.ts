import { useMemo } from 'react';
import { 
  colors, 
  componentColors, 
  chartColors, 
  gradients, 
  getColor, 
  getComponentColor,
  type ColorVariant,
  type ColorShade 
} from '@/lib/colors';

/**
 * Hook مخصص لإدارة الألوان في التطبيق
 * Custom hook for managing colors throughout the application
 */
export const useColors = () => {
  // دالة للحصول على ألوان الأولوية
  const getPriorityColors = useMemo(() => ({
    high: {
      className: `bg-[${componentColors.priority.high.bg}] text-[${componentColors.priority.high.text}] dark:bg-[${componentColors.priority.high.bgDark}] dark:text-[${componentColors.priority.high.textDark}]`,
      style: {
        backgroundColor: componentColors.priority.high.bg,
        color: componentColors.priority.high.text
      },
      darkStyle: {
        backgroundColor: componentColors.priority.high.bgDark,
        color: componentColors.priority.high.textDark
      }
    },
    medium: {
      className: `bg-[${componentColors.priority.medium.bg}] text-[${componentColors.priority.medium.text}] dark:bg-[${componentColors.priority.medium.bgDark}] dark:text-[${componentColors.priority.medium.textDark}]`,
      style: {
        backgroundColor: componentColors.priority.medium.bg,
        color: componentColors.priority.medium.text
      },
      darkStyle: {
        backgroundColor: componentColors.priority.medium.bgDark,
        color: componentColors.priority.medium.textDark
      }
    },
    low: {
      className: `bg-[${componentColors.priority.low.bg}] text-[${componentColors.priority.low.text}] dark:bg-[${componentColors.priority.low.bgDark}] dark:text-[${componentColors.priority.low.textDark}]`,
      style: {
        backgroundColor: componentColors.priority.low.bg,
        color: componentColors.priority.low.text
      },
      darkStyle: {
        backgroundColor: componentColors.priority.low.bgDark,
        color: componentColors.priority.low.textDark
      }
    }
  }), []);

  // دالة للحصول على ألوان حالة المشاريع
  const getProjectStatusColors = useMemo(() => ({
    active: {
      className: `bg-[${componentColors.projectStatus.active.bg}] text-[${componentColors.projectStatus.active.text}] dark:bg-[${componentColors.projectStatus.active.bgDark}] dark:text-[${componentColors.projectStatus.active.textDark}]`,
      style: {
        backgroundColor: componentColors.projectStatus.active.bg,
        color: componentColors.projectStatus.active.text
      }
    },
    completed: {
      className: `bg-[${componentColors.projectStatus.completed.bg}] text-[${componentColors.projectStatus.completed.text}] dark:bg-[${componentColors.projectStatus.completed.bgDark}] dark:text-[${componentColors.projectStatus.completed.textDark}]`,
      style: {
        backgroundColor: componentColors.projectStatus.completed.bg,
        color: componentColors.projectStatus.completed.text
      }
    },
    pending: {
      className: `bg-[${componentColors.projectStatus.pending.bg}] text-[${componentColors.projectStatus.pending.text}] dark:bg-[${componentColors.projectStatus.pending.bgDark}] dark:text-[${componentColors.projectStatus.pending.textDark}]`,
      style: {
        backgroundColor: componentColors.projectStatus.pending.bg,
        color: componentColors.projectStatus.pending.text
      }
    }
  }), []);

  // دالة للحصول على ألوان البطاقات الإحصائية
  const getStatsCardColors = useMemo(() => ({
    primary: {
      className: `bg-[${componentColors.statsCards.primary.bg}] border-[${componentColors.statsCards.primary.border}] text-[${componentColors.statsCards.primary.icon}] dark:bg-[${componentColors.statsCards.primary.bgDark}] dark:border-[${componentColors.statsCards.primary.borderDark}] dark:text-[${componentColors.statsCards.primary.iconDark}]`,
      style: {
        backgroundColor: componentColors.statsCards.primary.bg,
        borderColor: componentColors.statsCards.primary.border,
        color: componentColors.statsCards.primary.icon
      }
    },
    secondary: {
      className: `bg-[${componentColors.statsCards.secondary.bg}] border-[${componentColors.statsCards.secondary.border}] text-[${componentColors.statsCards.secondary.icon}] dark:bg-[${componentColors.statsCards.secondary.bgDark}] dark:border-[${componentColors.statsCards.secondary.borderDark}] dark:text-[${componentColors.statsCards.secondary.iconDark}]`,
      style: {
        backgroundColor: componentColors.statsCards.secondary.bg,
        borderColor: componentColors.statsCards.secondary.border,
        color: componentColors.statsCards.secondary.icon
      }
    },
    success: {
      className: `bg-[${componentColors.statsCards.success.bg}] border-[${componentColors.statsCards.success.border}] text-[${componentColors.statsCards.success.icon}] dark:bg-[${componentColors.statsCards.success.bgDark}] dark:border-[${componentColors.statsCards.success.borderDark}] dark:text-[${componentColors.statsCards.success.iconDark}]`,
      style: {
        backgroundColor: componentColors.statsCards.success.bg,
        borderColor: componentColors.statsCards.success.border,
        color: componentColors.statsCards.success.icon
      }
    },
    neutral: {
      className: `bg-[${componentColors.statsCards.neutral.bg}] border-[${componentColors.statsCards.neutral.border}] text-[${componentColors.statsCards.neutral.icon}] dark:bg-[${componentColors.statsCards.neutral.bgDark}] dark:border-[${componentColors.statsCards.neutral.borderDark}] dark:text-[${componentColors.statsCards.neutral.iconDark}]`,
      style: {
        backgroundColor: componentColors.statsCards.neutral.bg,
        borderColor: componentColors.statsCards.neutral.border,
        color: componentColors.statsCards.neutral.icon
      }
    }
  }), []);

  return {
    // الألوان الأساسية
    colors,
    getColor,
    
    // ألوان المكونات
    componentColors,
    getComponentColor,
    
    // ألوان محددة مسبقاً
    priority: getPriorityColors,
    projectStatus: getProjectStatusColors,
    statsCards: getStatsCardColors,
    
    // ألوان الرسوم البيانية
    chart: chartColors,
    
    // التدرجات
    gradients,
    
    // دوال مساعدة
    utils: {
      // دالة للحصول على لون بناءً على الحالة
      getStatusColor: (status: 'success' | 'warning' | 'error' | 'info') => {
        switch (status) {
          case 'success': return colors.success[500];
          case 'warning': return colors.warning[500];
          case 'error': return colors.error[500];
          case 'info': return colors.primary[500];
          default: return colors.neutral[500];
        }
      },
      
      // دالة للحصول على لون مع شفافية
      withOpacity: (color: string, opacity: number) => `${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`,
      
      // دالة للحصول على لون متباين
      getContrastColor: (backgroundColor: string) => {
        // منطق بسيط لتحديد اللون المتباين
        return backgroundColor.includes('900') || backgroundColor.includes('800') || backgroundColor.includes('700') 
          ? colors.neutral[50] 
          : colors.neutral[900];
      }
    }
  };
};

export default useColors;
