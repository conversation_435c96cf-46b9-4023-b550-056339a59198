/**
 * Zenith Flow - Unified Color System
 * مركز الألوان الموحد لتطبيق Zenith Flow
 */

// الألوان الأساسية - Primary Colors
export const colors = {
  // الألوان الأساسية
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe', 
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9', // اللون الأساسي الأزرق
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e'
  },

  // الألوان الثانوية - رمادي دافئ
  secondary: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a'
  },

  // ألوان الحالة - Status Colors
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d'
  },

  warning: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a'
  },

  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d'
  },

  // ألوان محايدة - Neutral Colors
  neutral: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717'
  }
} as const;

// أنواع الألوان للمكونات - Component Color Types
export type ColorVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'neutral';
export type ColorShade = 50 | 100 | 200 | 300 | 400 | 500 | 600 | 700 | 800 | 900;

// دالة للحصول على لون محدد
export const getColor = (variant: ColorVariant, shade: ColorShade = 500): string => {
  return colors[variant][shade];
};

// ألوان المكونات المحددة مسبقاً - Predefined Component Colors
export const componentColors = {
  // ألوان الأولوية
  priority: {
    high: {
      bg: colors.error[100],
      text: colors.error[700],
      bgDark: `${colors.error[900]}30`, // 30% opacity
      textDark: colors.error[400]
    },
    medium: {
      bg: colors.warning[100],
      text: colors.warning[700],
      bgDark: `${colors.warning[900]}30`,
      textDark: colors.warning[400]
    },
    low: {
      bg: colors.success[100],
      text: colors.success[700],
      bgDark: `${colors.success[900]}30`,
      textDark: colors.success[400]
    }
  },

  // ألوان حالة المشاريع
  projectStatus: {
    active: {
      bg: colors.primary[100],
      text: colors.primary[700],
      bgDark: `${colors.primary[900]}30`,
      textDark: colors.primary[400]
    },
    completed: {
      bg: colors.success[100],
      text: colors.success[700],
      bgDark: `${colors.success[900]}30`,
      textDark: colors.success[400]
    },
    pending: {
      bg: colors.warning[100],
      text: colors.warning[700],
      bgDark: `${colors.warning[900]}30`,
      textDark: colors.warning[400]
    }
  },

  // ألوان البطاقات الإحصائية
  statsCards: {
    primary: {
      bg: colors.primary[50],
      border: colors.primary[200],
      icon: colors.primary[600],
      bgDark: `${colors.primary[900]}30`,
      borderDark: colors.primary[700],
      iconDark: colors.primary[400]
    },
    secondary: {
      bg: colors.secondary[50],
      border: colors.secondary[200],
      icon: colors.secondary[600],
      bgDark: `${colors.secondary[900]}30`,
      borderDark: colors.secondary[700],
      iconDark: colors.secondary[400]
    },
    success: {
      bg: colors.success[50],
      border: colors.success[200],
      icon: colors.success[600],
      bgDark: `${colors.success[900]}30`,
      borderDark: colors.success[700],
      iconDark: colors.success[400]
    },
    neutral: {
      bg: colors.neutral[50],
      border: colors.neutral[200],
      icon: colors.neutral[600],
      bgDark: `${colors.neutral[900]}30`,
      borderDark: colors.neutral[700],
      iconDark: colors.neutral[400]
    }
  }
} as const;

// ألوان الرسوم البيانية - Chart Colors
export const chartColors = {
  primary: colors.primary[500],
  secondary: colors.secondary[500],
  success: colors.success[500],
  warning: colors.warning[500],
  error: colors.error[500],
  
  // مجموعة ألوان للرسوم البيانية المتعددة
  palette: [
    colors.primary[500],
    colors.secondary[500],
    colors.success[500],
    colors.neutral[500],
    colors.primary[300],
    colors.secondary[300]
  ]
} as const;

// التدرجات - Gradients
export const gradients = {
  primary: `linear-gradient(135deg, ${colors.primary[500]} 0%, ${colors.primary[600]} 100%)`,
  primarySoft: `linear-gradient(135deg, ${colors.primary[500]}20 0%, ${colors.primary[600]}20 100%)`,
  secondary: `linear-gradient(135deg, ${colors.secondary[500]} 0%, ${colors.secondary[600]} 100%)`,
  success: `linear-gradient(135deg, ${colors.success[500]} 0%, ${colors.success[600]} 100%)`
} as const;

// دوال مساعدة - Helper Functions
export const getComponentColor = (
  component: keyof typeof componentColors,
  variant: string,
  property: 'bg' | 'text' | 'bgDark' | 'textDark' | 'border' | 'borderDark' | 'icon' | 'iconDark'
): string => {
  const componentColor = componentColors[component] as any;
  return componentColor[variant]?.[property] || colors.neutral[500];
};

// CSS Variables للاستخدام في Tailwind
export const cssVariables = {
  '--color-primary': colors.primary[500],
  '--color-primary-foreground': colors.primary[50],
  '--color-secondary': colors.secondary[500],
  '--color-secondary-foreground': colors.secondary[50],
  '--color-success': colors.success[500],
  '--color-warning': colors.warning[500],
  '--color-error': colors.error[500],
  '--color-neutral': colors.neutral[500]
} as const;
