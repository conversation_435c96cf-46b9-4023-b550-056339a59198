# نظام الألوان الموحد - Zenith Flow Color System

## نظرة عامة
تم إنشاء نظام ألوان موحد ومركزي لتطبيق Zenith Flow لضمان الاتساق والسهولة في الصيانة.

## الملفات الأساسية

### 1. `src/lib/colors.ts`
يحتوي على جميع تعريفات الألوان والثوابت:
- الألوان الأساسية (Primary, Secondary, Success, Warning, Error, Neutral)
- ألوان المكونات المحددة مسبقاً
- ألوان الرسوم البيانية
- التدرجات
- دوال مساعدة

### 2. `src/hooks/useColors.ts`
Hook مخصص لاستخدام الألوان في المكونات:
- دوال للحصول على ألوان الأولوية
- دوال للحصول على ألوان حالة المشاريع
- دوال للحصول على ألوان البطاقات الإحصائية
- دوال مساعدة للألوان

## كيفية الاستخدام

### في المكونات
```tsx
import { useColors } from '@/hooks/useColors';

const MyComponent = () => {
  const { priority, projectStatus, statsCards, chart } = useColors();
  
  // استخدام ألوان الأولوية
  const priorityClass = priority.high.className;
  
  // استخدام ألوان حالة المشاريع
  const statusStyle = projectStatus.active.style;
  
  return (
    <div className={priorityClass}>
      <div style={statusStyle}>محتوى</div>
    </div>
  );
};
```

### الألوان المتاحة

#### ألوان الأولوية
- `priority.high` - أحمر للأولوية العالية
- `priority.medium` - أصفر للأولوية المتوسطة  
- `priority.low` - أخضر للأولوية المنخفضة

#### ألوان حالة المشاريع
- `projectStatus.active` - أزرق للمشاريع النشطة
- `projectStatus.completed` - أخضر للمشاريع المكتملة
- `projectStatus.pending` - أصفر للمشاريع المعلقة

#### ألوان البطاقات الإحصائية
- `statsCards.primary` - اللون الأساسي
- `statsCards.secondary` - اللون الثانوي
- `statsCards.success` - لون النجاح
- `statsCards.neutral` - لون محايد

#### ألوان الرسوم البيانية
- `chart.primary` - اللون الأساسي للرسوم البيانية
- `chart.secondary` - اللون الثانوي
- `chart.success` - لون النجاح
- `chart.palette` - مجموعة ألوان متنوعة

## المزايا

### 1. الاتساق
- جميع الألوان محددة في مكان واحد
- ضمان استخدام نفس الألوان في جميع أنحاء التطبيق

### 2. سهولة الصيانة
- تغيير لون واحد يؤثر على جميع المكونات
- لا حاجة للبحث في ملفات متعددة

### 3. دعم الوضع المظلم
- كل لون له نسخة للوضع المظلم
- تبديل تلقائي بين الأوضاع

### 4. TypeScript Support
- أنواع محددة لجميع الألوان
- IntelliSense كامل في IDE

## إرشادات الاستخدام

### ✅ افعل
- استخدم `useColors` hook في جميع المكونات
- استخدم الألوان المحددة مسبقاً
- اتبع نمط التسمية المحدد

### ❌ لا تفعل
- لا تستخدم ألوان hardcoded في المكونات
- لا تستخدم Tailwind colors مباشرة
- لا تنشئ ألوان جديدة خارج النظام

## إضافة ألوان جديدة

### 1. في `colors.ts`
```ts
export const componentColors = {
  // إضافة مكون جديد
  newComponent: {
    variant1: {
      bg: colors.primary[100],
      text: colors.primary[700],
      // ...
    }
  }
}
```

### 2. في `useColors.ts`
```ts
const getNewComponentColors = useMemo(() => ({
  variant1: {
    className: `bg-[${componentColors.newComponent.variant1.bg}] text-[${componentColors.newComponent.variant1.text}]`,
    style: {
      backgroundColor: componentColors.newComponent.variant1.bg,
      color: componentColors.newComponent.variant1.text
    }
  }
}), []);

return {
  // ...
  newComponent: getNewComponentColors
};
```

## الملفات المحدثة
تم تحديث الملفات التالية لاستخدام النظام الجديد:
- `src/components/Dashboard/TaskWidget.tsx`
- `src/components/Tasks/TasksList.tsx`
- `src/components/Projects/ProjectsGrid.tsx`
- `src/components/Dashboard/StatsCard.tsx`
- `src/components/Dashboard/Dashboard.tsx`
- `src/components/Analytics/AnalyticsDashboard.tsx`
- `src/components/Dashboard/ProductivityChart.tsx`

## التحديثات المستقبلية
- إضافة المزيد من الألوان حسب الحاجة
- تحسين دعم الوضع المظلم
- إضافة animations للألوان
- دعم themes متعددة
