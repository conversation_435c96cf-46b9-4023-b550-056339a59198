
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Zenith Flow - Effortless Productivity</title>
    <meta name="description" content="Transform your productivity with Zenith Flow - the ultimate task and time management application with AI-powered insights" />
    <meta name="author" content="Zenith Flow Team" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <meta property="og:title" content="Zenith Flow - Effortless Productivity" />
    <meta property="og:description" content="Transform your productivity with Zenith Flow - the ultimate task and time management application with AI-powered insights" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@zenithflow" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
    <style>
      /* Force override any yellow background with maximum specificity */
      html, body, #root {
        background-color: #f1f5f9 !important;
        background: #f1f5f9 !important;
        background-image: none !important;
      }

      /* Override any yellow/amber backgrounds */
      *[style*="yellow"], *[style*="amber"], *[class*="yellow"], *[class*="amber"] {
        background-color: #f1f5f9 !important;
        background: #f1f5f9 !important;
      }

      /* Ensure main container has proper background */
      .min-h-screen {
        background-color: #f1f5f9 !important;
        background: #f1f5f9 !important;
      }
    </style>
    <script>
      // Force background color with JavaScript
      document.addEventListener('DOMContentLoaded', function() {
        document.body.style.backgroundColor = '#f1f5f9';
        document.documentElement.style.backgroundColor = '#f1f5f9';

        // Monitor for any changes and override them
        const observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
              const target = mutation.target;
              if (target.style.backgroundColor &&
                  (target.style.backgroundColor.includes('yellow') ||
                   target.style.backgroundColor.includes('amber') ||
                   target.style.backgroundColor.includes('orange'))) {
                target.style.backgroundColor = '#f1f5f9';
              }
              if (target.style.borderColor &&
                  (target.style.borderColor.includes('yellow') ||
                   target.style.borderColor.includes('amber') ||
                   target.style.borderColor.includes('orange'))) {
                target.style.borderColor = '#e2e8f0';
              }
              if (target.style.color &&
                  (target.style.color.includes('yellow') ||
                   target.style.color.includes('amber') ||
                   target.style.color.includes('orange'))) {
                target.style.color = '#64748b';
              }
            }
          });
        });

        observer.observe(document.body, {
          attributes: true,
          subtree: true,
          attributeFilter: ['style']
        });
      });
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
