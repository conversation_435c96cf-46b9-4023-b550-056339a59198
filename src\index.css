
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Zenith Flow Design System - Modern OKLCH Professional Theme */
@layer base {
  :root {
    /* Modern OKLCH-based professional color scheme - WCAG AA Compliant */
    --background: 0.985 0.002 247.839;
    --foreground: 0.208 0.042 265.755;

    --card: 1 0 0;
    --card-foreground: 0.208 0.042 265.755;

    --popover: 1 0 0;
    --popover-foreground: 0.208 0.042 265.755;

    --primary: 0.623 0.214 259.815;
    --primary-foreground: 0.97 0.014 254.604;

    --secondary: 0.967 0.003 264.542;
    --secondary-foreground: 0.208 0.042 265.755;

    --muted: 0.967 0.003 264.542;
    --muted-foreground: 0.554 0.046 257.417;

    --accent: 0.967 0.003 264.542;
    --accent-foreground: 0.208 0.042 265.755;

    --destructive: 0.637 0.237 25.331;
    --destructive-foreground: 0.971 0.013 17.38;

    --border: 0.928 0.006 264.531;
    --input: 0.928 0.006 264.531;
    --ring: 0.623 0.214 259.815;

    --radius: 0.75rem;

    /* Sidebar theme variables */
    --sidebar-background: 1 0 0;
    --sidebar-foreground: 0.208 0.042 265.755;
    --sidebar-primary: 0.623 0.214 259.815;
    --sidebar-primary-foreground: 0.97 0.014 254.604;
    --sidebar-accent: 0.967 0.003 264.542;
    --sidebar-accent-foreground: 0.208 0.042 265.755;
    --sidebar-border: 0.928 0.006 264.531;
    --sidebar-ring: 0.623 0.214 259.815;
  }

  .dark {
    /* Dark theme - Professional blue-gray palette */
    --background: 0.208 0.042 265.755;
    --foreground: 0.985 0.002 247.839;

    --card: 0.279 0.041 260.031;
    --card-foreground: 0.985 0.002 247.839;

    --popover: 0.279 0.041 260.031;
    --popover-foreground: 0.985 0.002 247.839;

    --primary: 0.707 0.165 254.624;
    --primary-foreground: 0.208 0.042 265.755;

    --secondary: 0.372 0.044 257.287;
    --secondary-foreground: 0.985 0.002 247.839;

    --muted: 0.372 0.044 257.287;
    --muted-foreground: 0.707 0.022 261.325;

    --accent: 0.372 0.044 257.287;
    --accent-foreground: 0.985 0.002 247.839;

    --destructive: 0.704 0.191 22.216;
    --destructive-foreground: 0.971 0.013 17.38;

    --border: 0.372 0.044 257.287;
    --input: 0.446 0.043 257.281;
    --ring: 0.707 0.165 254.624;

    /* Dark sidebar theme */
    --sidebar-background: 0.279 0.041 260.031;
    --sidebar-foreground: 0.985 0.002 247.839;
    --sidebar-primary: 0.707 0.165 254.624;
    --sidebar-primary-foreground: 0.208 0.042 265.755;
    --sidebar-accent: 0.372 0.044 257.287;
    --sidebar-accent-foreground: 0.985 0.002 247.839;
    --sidebar-border: 0.372 0.044 257.287;
    --sidebar-ring: 0.707 0.165 254.624;
  }
}

/* Custom scrollbar for Zenith Flow - OKLCH Modern */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: oklch(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: oklch(var(--muted-foreground) / 0.5);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    min-height: 100vh;
  }

  /* Smooth transitions for all interactive elements */
  button, a, [role="button"] {
    @apply transition-all duration-200 ease-in-out;
  }

  /* Arabic font support */
  .font-arabic {
    font-family: 'Cairo', 'Amiri', 'Tajawal', sans-serif;
  }
}

/* Custom utility classes - Modern OKLCH System */
@layer utilities {
  .glass-effect {
    background: oklch(var(--card) / 0.8);
    backdrop-filter: blur(16px);
    border: 1px solid oklch(var(--border) / 0.2);
  }

  .card-hover {
    @apply hover:shadow-lg hover:scale-[1.02] transition-all duration-300 ease-out;
  }

  .zenith-gradient-text {
    background: linear-gradient(135deg,
      oklch(var(--primary)),
      oklch(var(--primary) / 0.8)
    );
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  .zenith-button {
    background: linear-gradient(135deg,
      oklch(var(--primary)),
      oklch(var(--primary) / 0.9)
    );
    @apply hover:shadow-lg transform hover:scale-105 active:scale-95 transition-all duration-200;
  }

  .zenith-float {
    animation: zenith-float 6s ease-in-out infinite;
  }

  .zenith-glow {
    animation: zenith-glow 3s ease-in-out infinite;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Enhanced animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zenithFloat {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg); 
  }
  50% { 
    transform: translateY(-10px) rotate(3deg); 
  }
}

@keyframes zenithGlow {
  0%, 100% { 
    box-shadow: 0 0 5px rgba(122, 143, 122, 0.3); 
  }
  50% { 
    box-shadow: 0 0 20px rgba(122, 143, 122, 0.5), 0 0 30px rgba(90, 111, 90, 0.3); 
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.4s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.animate-zenith-float {
  animation: zenithFloat 6s ease-in-out infinite;
}

.animate-zenith-glow {
  animation: zenithGlow 3s ease-in-out infinite;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .ml-auto {
  margin-left: 0;
  margin-right: auto;
}

[dir="rtl"] .mr-auto {
  margin-right: 0;
  margin-left: auto;
}

/* Focus styles for accessibility */
.focus-visible:focus {
  @apply ring-2 ring-primary ring-offset-2 ring-offset-background outline-none;
}

/* Loading animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Improved hover effects */
.hover-lift {
  @apply transform transition-transform duration-200 hover:-translate-y-1;
}

.hover-scale {
  @apply transform transition-transform duration-200 hover:scale-105;
}

/* Text selection */
::selection {
  background-color: hsl(var(--primary) / 0.2);
  color: hsl(var(--foreground));
}

/* Better focus indicators */
input:focus,
textarea:focus,
select:focus {
  @apply ring-2 ring-primary ring-offset-1 outline-none;
}

/* Smooth page transitions */
.page-transition {
  @apply transition-all duration-300 ease-in-out;
}
