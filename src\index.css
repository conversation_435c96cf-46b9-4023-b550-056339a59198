
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Zenith Flow Design System - Sky Blue & Soft Orange Theme */
@layer base {
  :root {
    /* Modern sky blue and soft orange color scheme */
    --background: 241 245 249;
    --foreground: 15 23 42;

    --card: 255 255 255;
    --card-foreground: 15 23 42;

    --popover: 255 255 255;
    --popover-foreground: 15 23 42;

    --primary: 59 130 246;
    --primary-foreground: 239 246 255;

    --secondary: 100 116 139;
    --secondary-foreground: 248 250 252;

    --muted: 226 232 240;
    --muted-foreground: 100 116 139;

    --accent: 241 245 249;
    --accent-foreground: 15 23 42;

    --destructive: 239 68 68;
    --destructive-foreground: 254 242 242;

    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 59 130 246;

    --radius: 0.75rem;

    --sidebar-background: 255 255 255;
    --sidebar-foreground: 15 23 42;
    --sidebar-primary: 59 130 246;
    --sidebar-primary-foreground: 239 246 255;
    --sidebar-accent: 241 245 249;
    --sidebar-accent-foreground: 15 23 42;
    --sidebar-border: 226 232 240;
    --sidebar-ring: 59 130 246;
  }

  .dark {
    --background: 15 23 42;
    --foreground: 248 250 252;

    --card: 30 41 59;
    --card-foreground: 248 250 252;

    --popover: 30 41 59;
    --popover-foreground: 248 250 252;

    --primary: 96 165 250;
    --primary-foreground: 30 58 138;

    --secondary: 148 163 184;
    --secondary-foreground: 15 23 42;

    --muted: 51 65 85;
    --muted-foreground: 148 163 184;

    --accent: 51 65 85;
    --accent-foreground: 248 250 252;

    --destructive: 248 113 113;
    --destructive-foreground: 127 29 29;

    --border: 51 65 85;
    --input: 51 65 85;
    --ring: 96 165 250;

    --sidebar-background: 30 41 59;
    --sidebar-foreground: 248 250 252;
    --sidebar-primary: 96 165 250;
    --sidebar-primary-foreground: 30 58 138;
    --sidebar-accent: 51 65 85;
    --sidebar-accent-foreground: 248 250 252;
    --sidebar-border: 51 65 85;
    --sidebar-ring: 96 165 250;
  }
}

/* Custom scrollbar for Zenith Flow */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    background: #f1f5f9 !important;
    background-color: #f1f5f9 !important;
    min-height: 100vh;
  }

  html {
    background: #f1f5f9 !important;
    background-color: #f1f5f9 !important;
  }

  #root {
    background: #f1f5f9 !important;
    background-color: #f1f5f9 !important;
  }

  /* Force override any yellow/amber/orange backgrounds */
  * {
    background-color: inherit !important;
  }

  *[style*="yellow"], *[style*="amber"], *[style*="orange"] {
    background-color: #f1f5f9 !important;
    background: #f1f5f9 !important;
  }

  .bg-yellow-50, .bg-yellow-100, .bg-yellow-200, .bg-yellow-300, .bg-yellow-400, .bg-yellow-500,
  .bg-amber-50, .bg-amber-100, .bg-amber-200, .bg-amber-300, .bg-amber-400, .bg-amber-500,
  .bg-orange-50, .bg-orange-100, .bg-orange-200, .bg-orange-300, .bg-orange-400, .bg-orange-500 {
    background-color: #f1f5f9 !important;
    background: #f1f5f9 !important;
  }

  /* Force override any yellow/amber/orange borders */
  .border-yellow-50, .border-yellow-100, .border-yellow-200, .border-yellow-300, .border-yellow-400, .border-yellow-500,
  .border-amber-50, .border-amber-100, .border-amber-200, .border-amber-300, .border-amber-400, .border-amber-500,
  .border-orange-50, .border-orange-100, .border-orange-200, .border-orange-300, .border-orange-400, .border-orange-500,
  .border-sky-50, .border-sky-100, .border-sky-200, .border-sky-300, .border-sky-400, .border-sky-500 {
    border-color: #e2e8f0 !important;
  }

  /* Force override any yellow/amber/orange text colors */
  .text-yellow-50, .text-yellow-100, .text-yellow-200, .text-yellow-300, .text-yellow-400, .text-yellow-500,
  .text-amber-50, .text-amber-100, .text-amber-200, .text-amber-300, .text-amber-400, .text-amber-500,
  .text-orange-50, .text-orange-100, .text-orange-200, .text-orange-300, .text-orange-400, .text-orange-500 {
    color: #64748b !important;
  }

  /* Smooth transitions for all interactive elements */
  button, a, [role="button"] {
    @apply transition-all duration-200 ease-in-out;
  }

  /* Arabic font support */
  .font-arabic {
    font-family: 'Cairo', 'Amiri', 'Tajawal', sans-serif;
  }
}

/* Custom utility classes */
@layer utilities {
  .glass-effect {
    @apply bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg border border-white/20 dark:border-gray-700/30;
  }

  .card-hover {
    @apply hover:shadow-card-hover hover:scale-[1.02] transition-all duration-300 ease-out;
  }

  .zenith-gradient-text {
    @apply bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent;
  }

  .zenith-button {
    @apply bg-zenith-gradient hover:shadow-zenith-lg transform hover:scale-105 active:scale-95 transition-all duration-200;
  }

  .zenith-float {
    animation: zenith-float 6s ease-in-out infinite;
  }

  .zenith-glow {
    animation: zenith-glow 3s ease-in-out infinite;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Enhanced animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zenithFloat {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg); 
  }
  50% { 
    transform: translateY(-10px) rotate(3deg); 
  }
}

@keyframes zenithGlow {
  0%, 100% { 
    box-shadow: 0 0 5px rgba(122, 143, 122, 0.3); 
  }
  50% { 
    box-shadow: 0 0 20px rgba(122, 143, 122, 0.5), 0 0 30px rgba(90, 111, 90, 0.3); 
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.4s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.animate-zenith-float {
  animation: zenithFloat 6s ease-in-out infinite;
}

.animate-zenith-glow {
  animation: zenithGlow 3s ease-in-out infinite;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .ml-auto {
  margin-left: 0;
  margin-right: auto;
}

[dir="rtl"] .mr-auto {
  margin-right: 0;
  margin-left: auto;
}

/* Focus styles for accessibility */
.focus-visible:focus {
  @apply ring-2 ring-primary ring-offset-2 ring-offset-background outline-none;
}

/* Loading animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Improved hover effects */
.hover-lift {
  @apply transform transition-transform duration-200 hover:-translate-y-1;
}

.hover-scale {
  @apply transform transition-transform duration-200 hover:scale-105;
}

/* Text selection */
::selection {
  background-color: hsl(var(--primary) / 0.2);
  color: hsl(var(--foreground));
}

/* Better focus indicators */
input:focus,
textarea:focus,
select:focus {
  @apply ring-2 ring-primary ring-offset-1 outline-none;
}

/* Smooth page transitions */
.page-transition {
  @apply transition-all duration-300 ease-in-out;
}
